const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabase() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test users table
    console.log('📋 Checking users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(5);
    
    if (usersError) {
      console.error('❌ Users table error:', usersError);
    } else {
      console.log('✅ Users table accessible, found', users.length, 'users');
      if (users.length > 0) {
        console.log('👤 Sample user:', users[0]);
      }
    }

    // Test master_child_relationships table
    console.log('📋 Checking master_child_relationships table...');
    const { data: relationships, error: relationshipsError } = await supabase
      .from('master_child_relationships')
      .select('*')
      .limit(5);
    
    if (relationshipsError) {
      console.error('❌ Relationships table error:', relationshipsError);
    } else {
      console.log('✅ Relationships table accessible, found', relationships.length, 'relationships');
    }

    // Test invitations table
    console.log('📋 Checking invitations table...');
    const { data: invitations, error: invitationsError } = await supabase
      .from('invitations')
      .select('*')
      .limit(5);
    
    if (invitationsError) {
      console.error('❌ Invitations table error:', invitationsError);
    } else {
      console.log('✅ Invitations table accessible, found', invitations.length, 'invitations');
    }

  } catch (error) {
    console.error('💥 Database test failed:', error);
  }
}

testDatabase();
