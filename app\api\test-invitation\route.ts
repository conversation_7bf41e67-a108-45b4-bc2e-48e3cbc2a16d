import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking existing invitations...');
    
    const supabase = await createClient();
    
    // Get all invitations with details
    const { data: invitations, error: invitationsError } = await supabase
      .from('invitations')
      .select('*')
      .order('createdAt', { ascending: false });
    
    if (invitationsError) {
      console.error('❌ Invitations query error:', invitationsError);
      return NextResponse.json(
        { error: invitationsError.message },
        { status: 500 }
      );
    }

    console.log('📧 Found invitations:', invitations);

    return NextResponse.json({
      success: true,
      invitations: invitations.map(inv => ({
        id: inv.id,
        senderId: inv.senderId,
        receiverEmail: inv.receiverEmail,
        status: inv.status,
        token: inv.token,
        expiresAt: inv.expiresAt,
        createdAt: inv.createdAt,
        demoConnectUrl: `http://localhost:3000/auth/demo-connect?token=${inv.token}`
      }))
    });

  } catch (error) {
    console.error('💥 Test invitation failed:', error);
    return NextResponse.json(
      { error: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
