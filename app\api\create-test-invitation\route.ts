import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import jwt from 'jsonwebtoken';

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Creating test invitation...');

    const supabase = await createClient();

    // Get the master user
    const { data: masterUser, error: masterError } = await supabase
      .from('users')
      .select('*')
      .eq('role', 'MASTER')
      .single();

    if (masterError || !masterUser) {
      return NextResponse.json(
        { error: 'No master user found' },
        { status: 404 }
      );
    }

    const testChildEmail = '<EMAIL>';

    // Create JWT invitation token
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    const token = jwt.sign(
      {
        masterEmail: masterUser.email,
        childEmail: testChildEmail,
        masterId: masterUser.id,
        type: 'invitation',
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    console.log('🎫 Created JWT token:', token);
    console.log('📧 Test child email:', testChildEmail);
    console.log('👤 Master user:', masterUser.email);

    return NextResponse.json({
      success: true,
      token: token,
      demoConnectUrl: `http://localhost:3000/auth/demo-connect?token=${token}`,
      invitationData: {
        masterEmail: masterUser.email,
        childEmail: testChildEmail,
        masterId: masterUser.id,
        type: 'invitation'
      }
    });

  } catch (error) {
    console.error('💥 Failed to create test invitation:', error);
    return NextResponse.json(
      { error: `Failed to create test invitation: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
