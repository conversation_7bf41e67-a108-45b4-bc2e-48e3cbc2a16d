import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { isDemoMode } from '@/app/config/demoMode';
import jwt from 'jsonwebtoken';

interface InvitationData {
  masterEmail: string;
  childEmail: string;
  masterId: string;
  type: string;
}

interface ChildUserData {
  email: string;
  name: string;
  role: 'child';
  zerodhaAccessToken: string;
  zerodhaRefreshToken: string;
  zerodhaUserId: string;
  masterId: string;
  masterEmail: string;
  invitationToken: string;
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Processing invitation acceptance...');

    const { token, childUserData } = await request.json();
    console.log('📝 Request data:', {
      hasToken: !!token,
      childEmail: childUserData?.email,
      childUserDataKeys: childUserData ? Object.keys(childUserData) : []
    });

    if (!token || !childUserData) {
      console.error('❌ Missing required data:', { hasToken: !!token, hasChildUserData: !!childUserData });
      return NextResponse.json(
        { error: 'Token and child user data are required' },
        { status: 400 }
      );
    }

    // Verify the invitation token
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    let invitationData: InvitationData;

    try {
      console.log('🔐 Verifying JWT token...');
      invitationData = jwt.verify(token, JWT_SECRET) as InvitationData;
      console.log('✅ JWT verified:', {
        masterEmail: invitationData.masterEmail,
        childEmail: invitationData.childEmail,
        masterId: invitationData.masterId,
        type: invitationData.type
      });

      if (!invitationData || invitationData.type !== 'invitation') {
        throw new Error('Invalid token format');
      }
    } catch (err) {
      console.error('❌ JWT verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid or expired invitation token' },
        { status: 400 }
      );
    }

    // Validate that the child email matches the invitation
    if (invitationData.childEmail !== childUserData.email) {
      console.error('❌ Email mismatch:', {
        invitationEmail: invitationData.childEmail,
        childUserEmail: childUserData.email
      });
      return NextResponse.json(
        { error: 'Child email does not match invitation' },
        { status: 400 }
      );
    }

    console.log('🔗 Connecting to Supabase...');
    const supabase = await createClient();
    const demoMode = isDemoMode();
    console.log('🎯 Demo mode:', demoMode);

    try {
      console.log('👤 Looking for master user:', invitationData.masterId);
      // Check if master user exists
      const { data: masterUser, error: masterError } = await supabase
        .from('users')
        .select('*')
        .eq('id', invitationData.masterId)
        .single();

      console.log('👤 Master user query result:', {
        found: !!masterUser,
        error: masterError?.message,
        masterEmail: masterUser?.email
      });

      if (masterError || !masterUser) {
        console.error('❌ Master user not found:', { masterId: invitationData.masterId, error: masterError });
        return NextResponse.json(
          { error: 'Master user not found' },
          { status: 404 }
        );
      }

      console.log('👶 Checking for existing child user:', childUserData.email);
      // Check if child user already exists
      let childUser;
      const { data: existingChild, error: childCheckError } = await supabase
        .from('users')
        .select('*')
        .eq('email', childUserData.email)
        .single();

      console.log('👶 Child user check result:', {
        found: !!existingChild,
        error: childCheckError?.message,
        childEmail: existingChild?.email
      });

      if (existingChild) {
        console.log('🔄 Updating existing child user...');
        // Update existing child user
        const { data: updatedChild, error: updateError } = await supabase
          .from('users')
          .update({
            name: childUserData.name,
            role: 'CHILD',
            updatedAt: new Date().toISOString()
          })
          .eq('email', childUserData.email)
          .select()
          .single();

        if (updateError) {
          console.error('❌ Error updating child user:', updateError);
          return NextResponse.json(
            { error: 'Failed to update child user' },
            { status: 500 }
          );
        }

        console.log('✅ Child user updated successfully');
        childUser = updatedChild;
      } else {
        console.log('➕ Creating new child user...');
        // Create new child user
        const { data: newChild, error: createError } = await supabase
          .from('users')
          .insert({
            email: childUserData.email,
            name: childUserData.name,
            role: 'CHILD',
            isDemo: true
          })
          .select()
          .single();

        if (createError) {
          console.error('❌ Error creating child user:', createError);
          return NextResponse.json(
            { error: 'Failed to create child user' },
            { status: 500 }
          );
        }

        console.log('✅ Child user created successfully');
        childUser = newChild;
      }

      console.log('🔗 Checking for existing master-child relationship...');
      // Create or update master-child relationship
      const { data: existingRelation, error: relationCheckError } = await supabase
        .from('master_child_relationships')
        .select('*')
        .eq('masterId', invitationData.masterId)
        .eq('childId', childUser.id)
        .single();

      console.log('🔗 Relationship check result:', {
        found: !!existingRelation,
        error: relationCheckError?.message
      });

      if (existingRelation) {
        console.log('🔄 Updating existing relationship...');
        // Update existing relationship
        const { error: updateRelationError } = await supabase
          .from('master_child_relationships')
          .update({
            isActive: true,
            connectedAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          })
          .eq('masterId', invitationData.masterId)
          .eq('childId', childUser.id);

        if (updateRelationError) {
          console.error('❌ Error updating master-child relationship:', updateRelationError);
          return NextResponse.json(
            { error: 'Failed to update master-child relationship' },
            { status: 500 }
          );
        }
        console.log('✅ Relationship updated successfully');
      } else {
        console.log('➕ Creating new relationship...');
        // Create new relationship
        const { error: createRelationError } = await supabase
          .from('master_child_relationships')
          .insert({
            masterId: invitationData.masterId,
            childId: childUser.id,
            isActive: true
          });

        if (createRelationError) {
          console.error('❌ Error creating master-child relationship:', createRelationError);
          return NextResponse.json(
            { error: 'Failed to create master-child relationship' },
            { status: 500 }
          );
        }
        console.log('✅ Relationship created successfully');
      }

      console.log('📧 Updating invitation status...');
      // Update invitation status to accepted
      const { error: invitationUpdateError } = await supabase
        .from('invitations')
        .update({
          status: 'ACCEPTED',
          acceptedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })
        .eq('token', token);

      if (invitationUpdateError) {
        console.error('⚠️ Error updating invitation status:', invitationUpdateError);
        // Don't fail the request for this, just log it
      } else {
        console.log('✅ Invitation status updated successfully');
      }

      console.log(`🎉 [${demoMode ? 'DEMO' : 'PROD'} MODE] Child user ${childUserData.email} successfully connected to master ${invitationData.masterEmail}`);

      return NextResponse.json({
        success: true,
        message: `Successfully connected child user to master${demoMode ? ' (Demo Mode)' : ''}`,
        childUser: {
          id: childUser.id,
          email: childUser.email,
          name: childUser.name,
          role: childUser.role
        },
        masterUser: {
          id: masterUser.id,
          email: masterUser.email,
          name: masterUser.name
        },
        demoMode
      });

    } catch (dbError) {
      console.error('💥 Database error:', dbError);
      return NextResponse.json(
        { error: `Database operation failed: ${dbError instanceof Error ? dbError.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('💥 Unexpected error accepting invitation:', error);
    return NextResponse.json(
      { error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
