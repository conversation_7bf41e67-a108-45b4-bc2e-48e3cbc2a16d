import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing database connection...');
    
    const supabase = await createClient();
    
    // Test users table
    console.log('📋 Checking users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(5);
    
    if (usersError) {
      console.error('❌ Users table error:', usersError);
    } else {
      console.log('✅ Users table accessible, found', users.length, 'users');
      if (users.length > 0) {
        console.log('👤 Sample user:', users[0]);
      }
    }

    // Test master_child_relationships table
    console.log('📋 Checking master_child_relationships table...');
    const { data: relationships, error: relationshipsError } = await supabase
      .from('master_child_relationships')
      .select('*')
      .limit(5);
    
    if (relationshipsError) {
      console.error('❌ Relationships table error:', relationshipsError);
    } else {
      console.log('✅ Relationships table accessible, found', relationships.length, 'relationships');
    }

    // Test invitations table
    console.log('📋 Checking invitations table...');
    const { data: invitations, error: invitationsError } = await supabase
      .from('invitations')
      .select('*')
      .limit(5);
    
    if (invitationsError) {
      console.error('❌ Invitations table error:', invitationsError);
    } else {
      console.log('✅ Invitations table accessible, found', invitations.length, 'invitations');
    }

    return NextResponse.json({
      success: true,
      results: {
        users: {
          count: users?.length || 0,
          error: usersError?.message || null
        },
        relationships: {
          count: relationships?.length || 0,
          error: relationshipsError?.message || null
        },
        invitations: {
          count: invitations?.length || 0,
          error: invitationsError?.message || null
        }
      }
    });

  } catch (error) {
    console.error('💥 Database test failed:', error);
    return NextResponse.json(
      { error: `Database test failed: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
